<!-- author:    xrybarj01 -->
<!DOCTYPE html>
<html lang="en">    <!-- cs -->
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta charset="utf-8">
        <title><PERSON><PERSON><PERSON></title>
        <link rel="stylesheet" href="css/style.css">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
        <script src="https://code.jquery.com/jquery-3.7.1.min.js"
        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="
        crossorigin="anonymous"></script>
        <link rel="icon" href="img/profile_pic_1.jpg" type="image/png">
        <script src="https://cdn.jsdelivr.net/npm/tsparticles@2/tsparticles.bundle.min.js"></script>
        <!-- EmailJS -->
        <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>
    </head>
<body>
    <header>
        <div class="menu-background">
            <div class="menu" data-state="rolled-in">
                <a href="https://www.stud.fit.vutbr.cz/~xrybarj01/" class="home-button">Jakub Rybář</a>
                <ul>
                    <li class="n1"><a href="#" class="scroll-section1">About</a></li>
                    <li class="n2"><a href="#" class="scroll-section2">Education</a></li>
                    <li class="n4"><a href="#" class="scroll-section3">Skills</a></li>
                    <li class="n3"><a href="#" class="scroll-section4">Experience</a></li>
                    <li class="n5"><a href="#" class="scroll-section5">Awards</a></li>
                    <li class="n6"><a href="#" class="scroll-section6">Portfolio</a></li>
                    <li class="n7"><a href="#" class="scroll-section7">Contact me <i class="fa-solid fa-paper-plane"></i></a></li>
                </ul>
                <button class="more" type="button">more</button>
            </div>
        </div>
        <div class="background">
            <div id="tsparticles"></div>
            <div class="sub">
                <div class="profile-pic">
                    <img src="img/profile_pic_1.jpg" alt="">
                </div>
                <div id="modal" class="modal">
                    <div class="modal-content">
                        <img src="img/profile_pic_1.jpg" alt="Profile Enlarged">
                    </div>
                </div>
                <div class="name">Jakub Rybář</div>
                <div class="major">Student, Electrician & Developer</div>
                <div class="download-cv">
                    <a href="file/JakubRybar.pdf" class="download-link" target="_blank">DOWNLOAD MY FULL RESUME <i class="fa fa-download"></i></a>
                </div>
                <div class="social-networks">
                    <ul>
                        <!-- <li><a target="_blank" href="https://www.instagram.com/jakub_rybar_/"><i class="fa-brands fa-instagram"></i></a></li> -->
                        <li><a target="_blank" href="https://www.facebook.com/profile.php?id=100013593971417" title="Jakub Rybář"><i class="fa-brands fa-facebook"></i></a></li>
                        <!-- <li><a target="_blank" href=""><i class="fa-brands fa-linkedin"></i></a></li> -->
                        <li><a target="_blank" href="https://discord.bio/p/jakub_79" title="jakub_79"><i class="fa-brands fa-discord"></i></a></li>
                        <li><a target="_blank" href="mailto:<EMAIL>" title="<EMAIL>"><i class="fa-solid fa-envelope"></i></a></li>
                        <li><a target="_blank" href="https://github.com/copp12" title="copp12"><i class="fa-brands fa-github"></i></a></li>
                    </ul>
                </div>
            </div>
        </div>
    </header>
    <main>
        <section class="about">
            <h2 class="none-heading">none</h2>
            <section class="about-info-wrapper">
                <div class="about-me">
                    <h2>ABOUT ME</h2>
                    <p>
                        Front-end and back-end developer. My primary programming languages are Python and C++. I am a student at the Faculty of Information Technology (FIT VUT), where I have just successfully completed my first year.
                    </p>
                </div>

                <div class="personal-info">
                    <h2>PERSONAL INFORMATION</h2>
                    <div class="info-grid">
                    <div class="info-item">
                        <strong>Full Name :</strong> <span>Jakub Rybář</span>
                    </div>
                    <div class="info-item">
                        <strong>Address :</strong> <span>Senetářov ***, 679 06 Senetářov</span>
                    </div>
                    <div class="info-item">
                        <strong>Age :</strong> <span>20 years old</span>
                    </div>
                    <div class="info-item">
                        <strong>Phone Number :</strong> <span>+420 777 448 676</span>
                    </div>
                    <div class="info-item">
                        <strong>Based In :</strong> <span>Czechia, South Moravian Region</span>
                    </div>
                    <div class="info-item">
                        <strong>Business Email :</strong> <span><EMAIL></span>
                    </div>
                    <div class="info-item">
                        <strong>Freelance :</strong> <span>Available</span>
                    </div>
                    <div class="info-item">
                        <strong>Other Languages :</strong> <span>English B2</span>
                    </div>
                    </div>
                </div>
                </section>
        </section>
        <section class="education">
            <div class="background-image"></div>
            <div class="sub">
                <h2>EDUCATION</h2>
                <div class="content">
                    <section>
                        <div class="main-info">
                            <div class="img2">
                                <img src="img/tega_logo.png" alt="">
                            </div>
                            <div class="sub2">
                                <h3><a href="https://sosblansko.cz/" target="_blank">SŠ Tega Blansko</a></h3>
                                <span class="date">2020 - 2024</span>
                            </div>
                        </div>
                        <p>
                            During my studies in the Low-voltage Electricia program, I participated in many professional competitions, which allowed me to deepen my technical knowledge and skills. I also had the opportunity to work on a project for TE Connectivity, gaining valuable experience in a real industrial environment.
                        </p>
                    </section>

                    <section>
                        <div class="main-info">
                            <div class="img2">
                                <img src="img/vut_logo.jpg" alt="">
                            </div>
                            <div class="sub2">
                                <h3><a href="https://www.fit.vut.cz/.cs" target="_blank">FIT VUT</a></h3>
                                <span class="date">2024 - Present</span>
                            </div>
                        </div>
                        <p>
                            I am currently studying at the Faculty of Information Technology at Brno University of Technology (FIT VUT), focusing on developing knowledge in information technology and programming. My studies provide me with up-to-date theoretical and practical skills needed for the modern IT industry.
                        </p>
                    </section>
                </div>
            </div>
        </section>
        
        <section class="skills">
            <h2>SKILLS</h2>
            <div class="skills-grid">
                <div class="skill">
                <div class="skill-header">
                    <i class="fa-brands fa-python"></i> Python <span>85%</span>
                </div>
                <div class="progress-bar"><div class="progress" style="width: 85%;"></div></div>
                <p>A highly readable and versatile scripting language, popular for data science, web development, and automation.</p>
                </div>

                <div class="skill">
                <div class="skill-header">
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/c.svg" alt="C logo"> C <span>70%</span>
                </div>
                <div class="progress-bar"><div class="progress" style="width: 70%;"></div></div>
                <p>A low-level language suitable for systems and firmware, highly efficient and close to hardware.</p>
                </div>

                <div class="skill">
                <div class="skill-header">
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/cplusplus.svg" alt="C++"> C++ <span>75%</span>
                </div>
                <div class="progress-bar"><div class="progress" style="width: 75%;"></div></div>
                <p> An extension of the C language with support for object-oriented programming, used in games, applications, and embedded systems.</p>
                </div>

                <div class="skill">
                <div class="skill-header">
                    <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons/icons/csharp.svg" alt="C++"> C# <span>65%</span>
                </div>
                <div class="progress-bar"><div class="progress" style="width: 65%;"></div></div>
                <p>A language for developing applications on the .NET platform, used in Windows desktop applications, web development, and game development.</p>
                </div>

                <div class="skill">
                <div class="skill-header">
                    <i class="fa-brands fa-js"></i> JavaScript, Html, CSS <span>70%</span>
                </div>
                <div class="progress-bar"><div class="progress" style="width: 75%;"></div></div>
                <p>A combination of languages for developing modern interactive websites.</p>
                </div>

                <div class="skill">
                <div class="skill-header">
                    <i class="fa-solid fa-cube"></i> SolidWorks <span>80%</span>
                </div>
                <div class="progress-bar"><div class="progress" style="width: 80%;"></div></div>
                <p>A program for creating advanced 3D models.</p>
                </div>
                
            </div>
        </section>
        <section class="experience">
            <h2>EXPERIENCE</h2>
            <div class="timeline">
                <section class="item" id="even">
                    <section class="main-info">
                        <a href="https://www.3id.cz/" target="_blank" class="sub">
                            <div class="company-info">
                                <h3>3id</h3>
                                <span class="date">2025</span>
                            </div>
                            <div class="company-logo">
                                <img src="img/3id_logo.png" alt="Marston s.r.o logo">
                            </div>
                        </a>
                    </section>
                    <section class="basic-info">
                        <div class="sub">
                            <h3 class="position">Software Engineer</h3>
                            <p>
                                Programming of an automated returnable cup machine using C++ and Python. Worked with PostgreSQL databases, FastAPI, and serial communication. Trained cup recognition models using PyTorch and deployed them on the machine.
                            </p>
                        </div>
                    </section>
                </section>
                <section class="item" id="odd">
                    <section class="main-info">
                        <a href="#" target="_blank" class="sub">
                            <div class="company-info">
                                <h3>Elektromontaze BARTEK</h3>
                                <span class="date">2024 - 2025</span>
                            </div>
                            <!-- <div class="company-logo"> -->
                                <!-- <img src="img/marston_logo.png" alt="Marston s.r.o logo"> -->
                            <!-- </div> -->
                        </a>
                    </section>
                    <section class="basic-info">
                        <div class="sub">
                            <h3 class="position">Electrical Technician</h3>
                            <p>
                                Construction electrician, installation, assembly, and wiring of electrical equipment and photovoltaic systems, including battery storage units.
                            </p>
                        </div>
                    </section>
                </section>
                <section class="item" id="even">
                    <section class="main-info">
                        <a href="https://www.marston.cz/" target="_blank" class="sub">
                            <div class="company-info">
                                <h3>Marston</h3>
                                <span class="date">2024</span>
                            </div>
                            <div class="company-logo">
                                <img src="img/marston_logo.png" alt="Marston s.r.o logo">
                            </div>
                        </a>
                    </section>
                    <section class="basic-info">
                        <div class="sub">
                            <h3 class="position">Machine Assembler</h3>
                            <p>Assembly of automated production machines</p>
                        </div>
                    </section>
                </section>
                
            </div>
        </section>
        <section class="awards">
            <h2>AWARDS</h2>
            <ul>
                <li> 2024 - Certificate according to Decree No. 250/2021 Coll. – professional qualification for work on electrical equipment..</li>
                <li> 2023 - 2nd place in the regional round of the 2023 Professional Skills Competition in the field of Electrical Engineering – Low Voltage.</li>
                <li> 2024 - High school leaving exam in the field of Electrician – Low Voltage.</li>
            </ul>
        </section>
        <section class="portfolio">
            <h2>PORTFOLIO</h2>
            <div class="projects">
                <section class="private">
                    <div class="content">
                        <div class="content-2">
                            <h3>Cup return machine - 3id</h3>
                            <div>
                                <p>
                                    In 3id I worked on the development of an automated returnable cup machine. I was responsible for the programming of the machine using C++ and Python. I worked with MySQL databases, python libraries such as FastAPI, RestAPI, Docker, Serial. I trained cup recognition models using PyTorch and deployed them on the machine.
                                </p>
                                <p>
                                    Unfortunately, the project is not public.
                                </p>
                                <a class="checkout">Private project <i class="fa-solid fa-lock"></i></a>
                            </div>
                        </div>
                        <div class="img2">
                            <div class="img3">
                                <img src="img/private_project.png" alt="">
                                <a class="img-label">Private project <i class="fa-solid fa-lock"></i></a>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="private">
                    <div class="content">
                        <div class="content-2">
                            <h3>Backend software for pickup boxes - 3id</h3>
                            <div>
                                <p>
                                    In 3id I also worked on the development of a embedded backend software for custom pickup boxes, which I was also responsible for. This software was written in Python and contains around 10000 lines of code. In this project I was using advanced python libraries such as FastAPI, MySQL, Docker and MQTT, Websockets, Asyncio, Threading, Requests, RestAPI.
                                </p>
                                <p>
                                    Unfortunately, the project is not public.
                                </p>
                                <a class="checkout">Private project <i class="fa-solid fa-lock"></i></a>
                            </div>
                        </div>
                        <div class="img2">
                            <div class="img3">
                                <img src="img/kvetomat.png" alt="">
                                <a class="img-label">Private project <i class="fa-solid fa-lock"></i></a>
                            </div>
                        </div>
                    </div>
                </section>
                <section>
                    <div class="content">
                        <div class="content-2">
                            <h3>GPU - school project</h3>
                            <div>
                                <p>
                                    I developed a GPU rendering pipeline simulation for my Computer Graphics (IZG) coursework, implementing vertex/fragment shaders, model rendering, and advanced lighting with shadow mapping in C++. Using SDL3 for windowing and GLM for math, I built a modular design separating the interface from custom implementations. The project includes full graphics pipeline stages—transformations, rasterization, texture sampling, fragment processing—plus testing suites and Doxygen documentation, showcasing strong skills in low-level graphics programming and 3D rendering math.
                                </p>
                                <a class="checkout" href="TODO" target="_blank">Check on github <i class="fab fa-github"></i></a>
                            </div>
                        </div>
                        <div class="img2">
                            <div class="img3">
                                <img src="img/rabbit.png" alt="">
                                <a class="img-label" href="TODO" target="_blank">Check on github <i class="fab fa-github"></i></a>
                            </div>
                        </div>
                    </div>
                </section>
                <!-- <section>
                    <div class="content">
                        <div class="content-2">
                            <h3>Automated Trading system</h3>
                            <div>
                                <div class="text-container">
                                    <p class="collapsible-text" id="tradingText">
                                        In my free time, I worked on an automated trading bot project. My largest project so far with with sum of 7000 linex of code. Through this project, I gained a lot of experience in programming. Using a single script and various APIs, I was able to trade across multiple brokers, including Interactive Brokers, Bybit, Binance, Phantom Wallet, IronFX, and NinjaTrader. <br>
                                        The system was built using the Python programming language. The code was not particularly clean, as it was intended solely for personal use and was constantly evolving. The bot could also be controlled remotely via Telegram, including from a mobile phone. Trade results were logged into Excel. <br>
                                        I focused primarily on low-liquidity markets, where simple strategies sometimes worked—but never in the long term. If I were to start over, I would definitely choose a faster programming language and a different development environment—such as NinjaTrader—where strategies can be backtested in seconds, tick data and order flow analysis can be utilized."
                                    </p>
                                </div>
                                <a class="checkout" href="https://github.com/copp12/Trading_bot/" target="_blank">Check on Github <i class="fab fa-github"></i></a>
                                <button class="toggle-button" onclick="toggleText()">
                                <span class="button-text">Show more</span>
                                <i class="fa-solid fa-chevron-down arrow"></i>
                                </button>
                            </div>
                        </div>
                        <div class="img2">
                            <div class="img3">
                                <img src="img/candles.png" alt="">
                                <a class="img-label" href="https://github.com/copp12/Trading_bot/" target="_blank">Check on Github <i class="fab fa-github"></i></a>
                            </div>
                        </div>
                    </div>
                </section> -->
                <section>
                    <div class="content">
                        <div class="content-2">
                            <h3>Robotic scanner</h3>
                            <div>
                                <p>
                                    In high school, I had the opportunity to participate in a competition where the task was to design a device with a robotic arm that scans individual components. I successfully created the design, including a 3D model, electronic schematics, and a budget plan. Unfortunately, I was unable to build the physical prototype due to preparation for my final exams.
                                </p>
                                <a class="checkout" href="https://grabcad.com/library/robotic-scanner-1" target="_blank">Check on GrabCad <i class="fas fa-cube"></i></a>
                            </div>
                        </div>
                        <div class="img2">
                            <div class="img3">
                                <img src="img/robotic_scanner.png" alt="">
                                <a class="img-label" href="https://grabcad.com/library/robotic-scanner-1" target="_blank">Check on GrabCad <i class="fas fa-cube"></i></a>
                            </div>
                        </div>
                    </div>
                </section>
                <section>
                    <div class="content">
                        <div class="content-2">
                            <h3>CV page</h3>
                            <div>
                                <p>
                                    A simple page with information about me, like an online résumé.
                                </p>
                                <a class="checkout" href="https://github.com/copp12/cv_page" target="_blank">Check on Github <i class="fab fa-github"></i></a>
                            </div>
                        </div>
                        <div class="img2">
                            <div class="img3">
                                <img src="img/cv_page.png" alt="">
                                <a class="img-label" href="https://github.com/copp12/cv_page" target="_blank">Check on Github <i class="fab fa-github"></i></a>
                            </div>
                        </div>
                    </div>
                </section>
                <section>
                    <div class="content">
                        <div class="content-2">
                            <h3>Line Following Robot</h3>
                            <div>
                                <p>
                                    In high school, I had the opportunity to participate in a line-following robot competition. I successfully created a 3D model, an electronic design, and a program in C++, as the robot used an Arduino Nano as its control unit.                                </p>
                                <a class="checkout" href="https://sketchfab.com/3d-models/sestava2-ca17b8cbedd6433e86fd080fc8dd8d92" target="_blank">Check on Sketchfab <i class="fas fa-cube"></i></a>
                            </div>
                        </div>
                        <div class="img2">
                            <div class="img3">
                                <img src="img/l_f_robot.png" alt="">
                                <a class="img-label" href="https://sketchfab.com/3d-models/sestava2-ca17b8cbedd6433e86fd080fc8dd8d92" target="_blank">Check on Sketchfab <i class="fas fa-cube"></i></a>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </section>
        <section class="contact">
            <div id="contact-particles"></div>
            <div class="sub-contact">
            <h2 class="con">Contact</h2>
                <section class="quick-id">
                    <h2>Contact me</h2>
                    <div class="contact-cards">
                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-info">
                                <h3>Phone</h3>
                                <p>+420 777 448 676</p>
                            </div>
                        </div>
                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-info">
                                <h3>E-mail</h3>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-info">
                                <h3>Address</h3>
                                <p>Senetářov ***, 679 06 Senetářov</p>
                            </div>
                        </div>
                    </div>
                </section>
                <section class="form">
                    <form class="contact-form" onsubmit="contact_submit(); return false;">
                        <fieldset class="name">
                            <fieldset>
                                <label for="first-name">First name *</label>
                                <input name="first-name" id="first-name" type="text" placeholder="Ethan" required>
                            </fieldset>
                            <fieldset>
                                <label for="last-name">Last name *</label>
                                <input name="last-name" id="last-name" type="text" placeholder="Brooks" required>
                            </fieldset>
                        </fieldset>
                        <label for="subject" >Subject *</label>
                        <input name="subject" id="subject" type="subject" placeholder="Subject" required>
                        <label for="email" >E-mail *</label>
                        <input name="email" id="email" type="email" placeholder="<EMAIL>" required>
                        <label for="message">Message *</label>
                        <textarea name="message" id="message" cols="30" rows="5" placeholder="Hello, I would like to ..."></textarea>
                        <button type="submit" id="submit">Send message <i class="fa-solid fa-paper-plane"></i></button>
                        <div class="submit-info">Got it!</div>
                    </form>
                </section>
            </div>
        </section>
    </main>
    <footer>
            <div class="download">
                <a href="file/JakubRybar.pdf" target="_blank">DOWNLOAD MY FULL RESUME <i class="fa fa-download"></i></a>
            </div>
            <div class="social-networks">
                <ul>
                    <!-- <li><a target="_blank" href="https://www.instagram.com/jakub_rybar_/"><i class="fa-brands fa-instagram"></i></a></li> -->
                    <li><a target="_blank" href="https://www.facebook.com/profile.php?id=100013593971417" title="Jakub Rybář"><i class="fa-brands fa-facebook"></i></a></li>
                    <!-- <li><a target="_blank" href=""><i class="fa-brands fa-linkedin"></i></a></li> -->
                    <li><a target="_blank" href="https://discord.bio/p/jakub_79" title="jakub_79"><i class="fa-brands fa-discord"></i></a></li>
                    <li><a target="_blank" href="mailto:<EMAIL>" title="<EMAIL>"><i class="fa-solid fa-envelope"></i></a></li>
                    <li><a target="_blank" href="https://github.com/copp12" title="copp12"><i class="fa-brands fa-github"></i></a></li>
                </ul>
                <p>Follow me on social media</p>
            </div>
    </footer>
    <div class="created-by">
        <div class="sub2">
            <p><i class="fa-regular fa-copyright"></i> 2025 Jakub Rybar</p>
            <p> <a href="#" class="website-link">Website Source Code</a></p>
        </div>
    </div>
    <button class="scroll-up" type="button"></button>

    <!-- live bacground -->
    <script>
        function setupParticlesBackground() {
            tsParticles.load("tsparticles", {
            fullScreen: { enable: false }, // nedělá fullscreen, protože to řešíš v CSS
            particles: {
                number: {
                value: 55,
                density: {
                    enable: true,
                    value_area: 800,
                }
                },
                color: {
                value: "#ffffff"
                },
                links: {
                enable: true,
                distance: 100,
                color: "#ffffff",
                opacity: 0.5,
                width: 1
                },
                move: {
                enable: true,
                speed: 2,
                direction: "none",
                outModes: {
                    default: "bounce"
                }
                },
                opacity: {
                value: 0.5
                },
                size: {
                value: 3
                }
            },
            retina_detect: true
            });
        }
        document.addEventListener("DOMContentLoaded", setupParticlesBackground);

        // Contact section particles
        function setupContactParticles() {
            tsParticles.load("contact-particles", {
                fullScreen: { enable: false },
                particles: {
                    number: {
                        value: 30,
                        density: {
                            enable: true,
                            value_area: 800
                        }
                    },
                    color: {
                        value: "#ffffff"
                    },
                    shape: {
                        type: "circle"
                    },
                    opacity: {
                        value: 0.1,
                        random: true
                    },
                    size: {
                        value: 3,
                        random: true
                    },
                    line_linked: {
                        enable: true,
                        distance: 150,
                        color: "#ffffff",
                        opacity: 0.05,
                        width: 1
                    },
                    move: {
                        enable: true,
                        speed: 1,
                        direction: "none",
                        random: false,
                        straight: false,
                        out_mode: "out",
                        bounce: false
                    }
                },
                interactivity: {
                    detect_on: "canvas",
                    events: {
                        onhover: {
                            enable: false
                        },
                        onclick: {
                            enable: false
                        },
                        resize: true
                    }
                },
                retina_detect: true
            });
        }

        document.addEventListener("DOMContentLoaded", setupContactParticles);
    </script>


    <!-- auto scroll -->
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Scroll to section when menu item is clicked
            document.querySelectorAll("[class^='scroll-section']").forEach(function (element) {
                element.addEventListener("click", function (e) {
                    e.preventDefault();

                    const match = this.className.match(/scroll-section(\d)/);
                    const sectionNumber = match ? match[1] : null;

                    const sectionMap = {
                        1: { selector: ".about", offset: 40 },
                        2: { selector: ".education", offset: 40 },
                        3: { selector: ".skills", offset: 40 },
                        4: { selector: ".experience", offset: 40 },
                        5: { selector: ".awards", offset: 40 },
                        6: { selector: ".portfolio", offset: 40 },
                        7: { selector: ".contact", offset: 40 },
                    };

                    const section = sectionMap[sectionNumber];
                    if (section) {
                        const target = document.querySelector(section.selector);
                        if (target) {
                            const scrollTo = target.getBoundingClientRect().top + window.pageYOffset - section.offset;
                            window.scrollTo({ top: scrollTo, behavior: "smooth" });
                        }
                    }
                });
            });

            // Scroll-up button
            const scrollUpBtn = document.querySelector(".scroll-up");
            if (scrollUpBtn) {
                scrollUpBtn.addEventListener("click", function (e) {
                    e.preventDefault();
                    window.scrollTo({ top: 0, behavior: "smooth" });
                });
            }
        });
    </script>

    <!-- main javascript script -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const menu = document.querySelector('header .menu');
            const moreButton = document.querySelector('.more');
            const menuLinks = document.querySelectorAll('header .menu ul li a');
            const scrollUp = document.querySelector('.scroll-up');

            const sections = [
                { selector: '.about', nav: 'n1' },
                { selector: '.education', nav: 'n2' },
                { selector: '.skills', nav: 'n4' },
                { selector: '.experience', nav: 'n3' },
                { selector: '.awards', nav: 'n5' },
                { selector: '.portfolio', nav: 'n6' },
                { selector: '.contact', nav: 'n7' }
            ];

            // Roll out / Roll in the menu
            moreButton?.addEventListener('click', () => {
                const state = menu.getAttribute('data-state');
                menu.setAttribute('data-state', state === 'rolled-out' ? 'rolled-in' : 'rolled-out');
            });

            // Roll back in the menu after clicking on section
            menuLinks.forEach(link =>
                link.addEventListener('click', () => {
                    if (menu.getAttribute('data-state') === 'rolled-out') {
                        menu.setAttribute('data-state', 'rolled-in');
                    }
                })
            );

            // change current scrolled section
            const items = document.querySelectorAll('header .menu ul li');
            const updateMenuHighlight = () => {
                let closest = null, closestOffset = Infinity;

                sections.forEach(({ selector, nav }) => {
                    const el = document.querySelector(selector);
                    if (el) {
                        const rect = el.getBoundingClientRect();
                        const offset = Math.abs(rect.top);
                        if (offset < closestOffset && rect.top <= window.innerHeight) {
                            closest = nav;
                            closestOffset = offset;
                        }
                    }
                });

                items.forEach(item => item.classList.remove('scrolled'));
                if (closest) {
                    document.querySelector(`header .menu ul li.${closest}`)?.classList.add('scrolled');
                }
            };


            // check if the website is scrolled at top
            const updateScrollTopClass = () => {
                if (!scrollUp) return;
                if (window.scrollY <= 40) {
                    document.documentElement.classList.add('at-top');
                } else {
                    document.documentElement.classList.remove('at-top');

                }
            };

            window.addEventListener('scroll', () => {
                updateMenuHighlight();
                updateScrollTopClass();
            });

            // Initial check on load
            updateMenuHighlight();
            updateScrollTopClass();
        });
    </script>


    <!-- EmailJS initialization and contact form handler -->
    <script>
        // Initialize EmailJS
        (function() {
            emailjs.init("8IYsKIBK8MHLXfVKZ"); // Your public key
        })();

        function contact_submit() {
            let form = document.querySelector(".contact-form");
            let submitButton = document.querySelector("#submit");

            if (form) {
                // Disable submit button to prevent multiple submissions
                submitButton.disabled = true;
                submitButton.innerHTML = 'Sending... <i class="fa-solid fa-spinner fa-spin"></i>';

                // Get form data
                const templateParams = {
                    first_name: document.getElementById('first-name').value,
                    last_name: document.getElementById('last-name').value,
                    subject: document.getElementById('subject').value,
                    from_email: document.getElementById('email').value,
                    message: document.getElementById('message').value
                };

                // Send email using EmailJS
                emailjs.send('service_gx4mrtp', 'template_w9jqjgm', templateParams)
                    .then(function(response) {
                        console.log('SUCCESS!', response.status, response.text);

                        // Show success message
                        form.classList.add("open");
                        document.querySelector(".submit-info").textContent = "Got it! I always reply within few hours.";

                        // Reset form
                        form.reset();

                        // Hide success message after 3 seconds
                        setTimeout(() => {
                            form.classList.remove("open");
                            submitButton.disabled = false;
                            submitButton.innerHTML = 'Send message <i class="fa-solid fa-paper-plane"></i>';
                        }, 5000);

                    }, function(error) {
                        console.log('FAILED...', error);

                        // Show error message
                        form.classList.add("open");
                        document.querySelector(".submit-info").textContent = "Failed to send message. Please try again.";
                        document.querySelector(".submit-info").style.background = "rgba(239, 68, 68, 0.15)";
                        document.querySelector(".submit-info").style.borderColor = "rgba(239, 68, 68, 0.3)";
                        document.querySelector(".submit-info").style.color = "rgb(239, 68, 68)";

                        // Hide error message after 3 seconds and reset button
                        setTimeout(() => {
                            form.classList.remove("open");
                            submitButton.disabled = false;
                            submitButton.innerHTML = 'Send message <i class="fa-solid fa-paper-plane"></i>';

                            // Reset error styling
                            document.querySelector(".submit-info").style.background = "rgba(34, 197, 94, 0.15)";
                            document.querySelector(".submit-info").style.borderColor = "rgba(34, 197, 94, 0.3)";
                            document.querySelector(".submit-info").style.color = "rgb(34, 197, 94)";
                            document.querySelector(".submit-info").textContent = "Got it!";
                        }, 3000);
                    });
            }
        }
    </script>


    <script>
        function toggleText() {
            const text = document.getElementById("tradingText");
            const textContainer = text.parentElement;
            const button = document.querySelector(".toggle-button");
            const buttonText = button.querySelector(".button-text");
            const arrow = button.querySelector(".arrow");

            if (text.classList.contains("expanded")) {
                const height = text.scrollHeight;
                text.style.maxHeight = height + 'px';
                setTimeout(() => {
                text.style.maxHeight = '6.5em';
                }, 10);
                text.classList.remove("expanded");
                textContainer.classList.remove("expanded");
                buttonText.textContent = "Show more";
                arrow.classList.remove("fa-chevron-up");
            arrow.classList.add("fa-chevron-down");
                
            } else {
                text.style.maxHeight = text.scrollHeight + 'px';
                text.classList.add("expanded");
                textContainer.classList.add("expanded");
                buttonText.textContent = "Show less";
                arrow.classList.remove("fa-chevron-down");
            arrow.classList.add("fa-chevron-up");

            }
            }
    </script>

    <script>
        const modal = document.getElementById("modal");
        const profilePic = document.querySelector(".profile-pic img");

        // Otevření modalu
        profilePic.addEventListener("click", () => {
            modal.classList.add("show");
            document.body.classList.add("modal-open");
        });

        // Zavření modalu klikem kamkoliv do modalu (včetně obrázku)
        modal.addEventListener("click", () => {
            modal.classList.remove("show");
            document.body.classList.remove("modal-open");
        });

        // Zavření modalu klávesou Escape
        document.addEventListener("keydown", (e) => {
            if (e.key === "Escape" && modal.classList.contains("show")) {
            modal.classList.remove("show");
            document.body.classList.remove("modal-open");
            }
        });
    </script>        
</body>